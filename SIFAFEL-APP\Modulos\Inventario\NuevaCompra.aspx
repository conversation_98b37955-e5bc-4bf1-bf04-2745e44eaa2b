<%@ Page Title="Nueva Compra" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="NuevaCompra.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.NuevaCompra" %>

<%@ Register Src="~/WebUserControls/WUC_InfoProduct.ascx" TagPrefix="uc1" TagName="WUC_InfoProduct" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
    <meta charset="utf-8" />
    <link href='<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/select2/select2.min.css' rel="stylesheet" />
    <script src='<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/select2/select2.min.js'></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <uc1:WUC_InfoProduct runat="server" ID="WUC_InfoProduct" />
    <meta charset="utf-8" />

    <%-- MODAL PROVEEDORES --%>
    <div class="modal fade effect-scale" id="modalProveedores">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title"><i data-feather="truck" class="feather-16"></i>&nbsp;Consultas de proveedores</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">

                    <table id="grvProveedores" class="table table-bordered table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>Código</th>
                                <th>NIT</th>
                                <th>Proveedor</th>
                                <th>Teléfono</th>
                                <th>Correo</th>
                                <th>Direccion</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>

                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- MODAL CREAR PROVEEDOR --%>
    <div class="modal fade effect-scale" id="modalCrearProveedor">
        <div class="modal-dialog modal-dialog-centered modal-md" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title"><i data-feather="user-plus" class="feather-16"></i>&nbsp;Crear Proveedor</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">
                    <form id="formCrearProveedor">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtNitNuevoProveedor" class="form-label">NIT <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="txtNitNuevoProveedor" name="txtNitNuevoProveedor" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtNombreNuevoProveedor" class="form-label">Nombre <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="txtNombreNuevoProveedor" name="txtNombreNuevoProveedor" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtTelefonoNuevoProveedor" class="form-label">Tel&eacute;fono</label>
                                    <input type="text" class="form-control" id="txtTelefonoNuevoProveedor" name="txtTelefonoNuevoProveedor">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtEmailNuevoProveedor" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="txtEmailNuevoProveedor" name="txtEmailNuevoProveedor">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="txtDireccionNuevoProveedor" class="form-label">Direcci&oacute;n</label>
                                    <textarea class="form-control" id="txtDireccionNuevoProveedor" name="txtDireccionNuevoProveedor" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" id="btnGuardarNuevoProveedor">
                        <i data-feather="save" class="feather-16"></i>&nbsp;Guardar
                   
                    </button>
                </div>
            </div>
        </div>
    </div>

    <%-- MODAL PRODUCTOS --%>
    <div class="modal fade effect-scale" id="modalProductos">
        <div class="modal-dialog modal-fullscreen" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title">Productos</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">
                    <table id="grvProductosConsulta" class="table table-bordered table-hover table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>C&oacute;digo</th>
                                <th>Producto</th>
                                <th>Precio unitario</th>
                                <th>Marca</th>
                                <th>Modelo</th>
                                <th>A&ntilde;o</th>
                                <th>Descripci&oacute;n</th>
                                <th>Existencia</th>
                                <th>En canasta</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- HEADER --%>
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="shopping-bag" class="me-2"></i>Nueva compra</h4>
                <h6>Crear nueva compra </h6>
            </div>
        </div>
        <div class="page-btn">
            <a href="Compras.aspx" class="btn btn-added color">
                <i data-feather="inbox" class="me-2"></i>Listado de compras
            </a>
        </div>
    </div>

    <%-- COMPRAS --%>
    <div class="row compras">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">

                    <div class="row">
                        <!-- SECCION DE PROVEEDOR -->
                        <div class="col-md-8">
                            <div class="form-horizontal">

                                <!-- NIT PROVEEDOR -->
                                <div class="mb-1 row">
                                    <div class="add-newplus">
                                        <span id="lblIdProveedor" hidden=""></span>
                                        <label for="txtNitProveedor" class="form-label">Proveedor:</label>

                                        <a href="#!" id="btnBuscarProveedor">
                                            <i data-feather="search" class="plus-down-add"></i>
                                            <span>Buscar</span>
                                        </a>
                                    </div>
                                    <div class="col-12">
                                        <div class="row g-0">
                                            <div class="col-4 input-h-control">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="truck" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtNitProveedor" name="txtNitProveedor" type="text" aria-label="NIT" placeholder="NIT" class="form-control no-right-border">
                                                </div>
                                            </div>
                                            <div class="col-8">
                                                <input id="txtNombreProveedor" type="text" aria-label="Nombre completo" placeholder="Nombre completo" class="form-control no-left-border input-h-control" disabled="disabled">
                                            </div>
                                        </div>
                                    </div>
                                </div>



                            </div>
                        </div>

                        <!-- SECCION DE COMPRA -->
                        <div class="col-md-4">
                            <div class="form-horizontal">

                                <!-- FECHA DE COMPRA -->
                                <div class="mb-1 row">
                                    <div class="col-sm-12">
                                        <label class="form-label">Fecha:</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">
                                                <i data-feather="calendar" class="feather-16"></i>
                                            </span>
                                            <input class="form-control" id="txtFechaCompra" name="txtFechaCompra" type="date" placeholder="Fecha Compra" />
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row segunda-fila">
                        <!-- SEGUNDA FILA RESPONSIVE -->
                        <div class="col-12">
                            <div class="form-horizontal">
                                <div class="mb-2 row">
                                    <div class="col-12">
                                        <div class="row g-2">
                                            <!-- Código de compra -->
                                            <div class="col-12 col-sm-6 col-md-4">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="file-text" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtCodigoCompra" name="txtCodigoCompra" type="text" aria-label="Codigo" placeholder="N&uacute;mero de factura" class="form-control">
                                                </div>
                                            </div>
                                            <!-- Numero de serie -->
                                            <div class="col-12 col-sm-6 col-md-3">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="file-text" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtNumSerie" name="txtNumSerie" type="text" aria-label="Codigo" placeholder="N&uacute;mero de serie" class="form-control">
                                                </div>
                                            </div>
                                            <!-- Forma de pago -->
                                            <div class="col-12 col-sm-12 col-md-3">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="credit-card" class="feather-16"></i>
                                                    </span>
                                                    <select id="ddlFormaPago" name="ddlFormaPago" class="form-control form-control-sm">
                                                        <option value="">Forma de pago</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <!-- CHECK DUCA-->
                                            <div class="col-12 col-sm-12 col-md-3">
                                                <div class="input-group input-group-sm mb-2">
                                                    <div class="form-check form-check-inline ms-2 mt-1">
                                                        <input class="form-check-input" type="checkbox" id="chkAplicaDuca">
                                                        <label class="form-check-label" for="chkAplicaDuca">&iquest;Aplica Duca?</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- SEGUNDA FILA -->
                                        <div class="row mb-3">

                                            <!-- INPUT PARA GUARDAR FACTURA -->
                                            <div class="col-12 col-sm-6 col-md-3" style="display: none;">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="upload" class="feather-16"></i>
                                                    </span>
                                                    <input id="fileFactura" name="fileFactura" type="file" accept="image/*,.pdf" aria-label="Factura" class="form-control" onchange="handleFileUpload(this)">
                                                </div>
                                            </div>

                                            <!-- Póliza/DUCA -->
                                            <div class="col-12 col-sm-6 col-md-3" style="display: none;">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="file-text" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtPolizaDuca" name="txtPolizaDuca" type="text" aria-label="DUCA No." placeholder="DUCA No." class="form-control">
                                                </div>
                                            </div>

                                            <!-- PAIS ORIGEN-->
                                            <div class="col-12 col-sm-12 col-md-3" style="display: none;">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="flag" class="feather-16"></i>
                                                    </span>
                                                    <select id="ddlPaisOrigen" class="form-control form-control-sm">
                                                        <option value="">Pa&iacute;s de origen</option>
                                                        <option value="1">Guatemala</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <!-- PAIS DESTINO-->
                                            <div class="col-12 col-sm-12 col-md-3" style="display: none;">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="map-pin" class="feather-16"></i>
                                                    </span>
                                                    <select id="ddlPaisDestino" class="form-control form-control-sm">
                                                        <option value="">Pa&iacute;s de destino</option>
                                                        <option value="1">Guatemala</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- TERCERA FILA -->
                                        <div class="row mb-3">
                                            <!-- DUCA NO ORDEN-->
                                            <div class="col-12 col-sm-12 col-md-3" style="display: none;">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="hash" class="feather-16"></i>
                                                    </span>
                                                    <input type="text" class="form-control form-control-sm" id="txtDucaNoOrden" placeholder="No. de orden">
                                                </div>
                                            </div>

                                            <!-- ENTRADA SALIDA PARTIDA-->
                                            <div class="col-12 col-sm-12 col-md-3" style="display: none;">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="repeat" class="feather-16"></i>
                                                    </span>
                                                    <input type="text" class="form-control form-control-sm" id="txtDucaEntradaSalidaPartida" placeholder="Entrada / Salida / Partida">
                                                </div>
                                            </div>

                                            <!-- DOMICILIO FISCAL-->
                                            <div class="col-12 col-sm-12 col-md-3" style="display: none;">
                                                <div class="input-group input-group-sm mb-2">
                                                    <span class="input-group-text">
                                                        <i data-feather="home" class="feather-16"></i>
                                                    </span>
                                                    <input type="text" class="form-control form-control-sm" id="txtDucaDomicilioFiscal" placeholder="Domicilio Fiscal">
                                                </div>
                                            </div>
                                        </div>



                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <span class="card-title" style="float: left; width: auto !important;">
                            <i data-feather="package" class="feather-16"></i>&nbsp;Productos
                        </span>

                        <%-- CODIGO DE BARRA --%>
                        <div class="col-lg-4 col-sm-12 ms-auto">
                            <div class="add-newplus">
                                <label class="form-label" for="txtCodigoBarraProducto">&nbsp;</label>
                                <a href="#!" id="btnBuscarProducto">
                                    <i data-feather="search" class="plus-down-add"></i>
                                    <span>Consultar</span>
                                </a>
                            </div>
                            <div class="input-blocks">
                                <div class="input-groupicon select-code">
                                    <input id="txtCodigoBarraProducto" name="txtCodigoBarraProducto" class="barcode-search" type="text" placeholder="C&oacute;digo de producto" style="padding: 10px;">
                                    <div class="addonset">
                                        <img src="<%=ConfigurationManager.AppSettings["url_cdn"]%>img/barcode-scanner.gif" alt="img" style="height: 38px;">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <%-- PRODUCTOS COMPRA --%>
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="grvProductosCompra" class="table table-striped table-bordered table-sm">
                                <thead class="table-head">
                                    <tr>
                                        <th>Id</th>
                                        <th>C&oacute;digo</th>
                                        <th>Producto</th>
                                        <th>Cantidad</th>
                                        <th>Existencia</th>
                                        <th>Costo U.</th>
                                        <th>Costo Total</th>
                                        <th>Precio Venta</th>
                                        <th>Precio M&iacute;nimo</th>
                                        <th>&nbsp;</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-sm-12 col-md-6 col-lg-4 ms-md-auto mt-2">
                            <div class="total-order w-100 max-widthauto m-auto mb-1">
                                <ul>
                                    <li class="total">
                                        <h4>Total a pagar</h4>
                                        <h4><span class="lbl-info-moneda">Q.</span>&nbsp;<span class="lbl-info-total">0.00</span></h4>
                                    </li>
                                </ul>
                            </div>



                            <div class="btn-row d-sm-flex align-items-center justify-content-between mb-4">
                                <a id="btnGuardarCompraFinal" class="btn btn-success btn-icon flex-fill">
                                    <span class="me-1 d-flex align-items-center">
                                        <i data-feather="save" class="feather-16"></i>
                                    </span>
                                    Guardar Compra    
                                </a>
                                <a id="btnCancelarCompra" class="btn btn-danger btn-icon flex-fill">
                                    <span class="me-1 d-flex align-items-center">
                                        <i data-feather="trash-2" class="feather-16"></i>
                                    </span>
                                    Cancelar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <style>
        /* Estilos para inputs sin bordes */
        .no-left-border {
            border-left: none !important;
            border-radius: 0 !important;
        }

        .no-right-border {
            border-right: none !important;
            border-radius: 0 !important;
        }

        .input-h-control {
            height: 38px;
        }

        /* Estilos para DataTable */
        #grvProductosCompra td {
            vertical-align: middle !important;
            padding: 4px 6px !important;
            height: 35px !important;
        }

        #grvProductosCompra .form-control {
            height: 28px !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
            border: 1px solid #ced4da !important;
            border-radius: 4px !important;
        }

        #grvProductosCompra .btn-sm {
            padding: 4px 8px !important;
            font-size: 12px !important;
        }

        .productimgname img {
            width: 32px !important;
            height: 32px !important;
            object-fit: cover;
            border-radius: 4px;
        }

        .view-product img {
            width: 32px !important;
            height: 32px !important;
            object-fit: cover;
            border-radius: 4px;
        }

        .view-product {
            display: inline-block;
            vertical-align: middle;
        }

        .view-info-product {
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            font-size: 13px;
            text-decoration: none;
            color: #495057;
            white-space: normal !important;
            word-wrap: break-word !important;
            line-height: 1.3;
        }

            .view-info-product:hover {
                color: #007bff;
                text-decoration: none;
            }

        @media (max-width: 576px) {
            .segunda-fila .col-12 {
                margin-bottom: 0.5rem;
            }

            .segunda-fila .input-group-sm .form-control,
            .segunda-fila .input-group-sm .form-select {
                font-size: 0.875rem;
                padding: 0.375rem 0.75rem;
            }
        }

        @media (min-width: 577px) and (max-width: 768px) {
            .segunda-fila .col-sm-6 {
                flex: 0 0 50%;
                max-width: 50%;
            }

            .segunda-fila .col-sm-12 {
                flex: 0 0 100%;
                max-width: 100%;
                margin-top: 0.5rem;
            }
        }

        @media (min-width: 769px) {
            .segunda-fila .col-md-4 {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
            }
        }


        .segunda-fila .input-group {
            margin-bottom: 0;
        }

        .segunda-fila .row.g-2 {
            --bs-gutter-x: 0.5rem;
            --bs-gutter-y: 0.5rem;
        }

        .segunda-fila .input-group-text {
            min-width: 2.5rem;
            justify-content: center;
        }

        .segunda-fila .feather-16 {
            width: 16px;
            height: 16px;
        }

        /* Estilos para la tabla de productos */
        #grvProductosCompra .productimgname {
            white-space: normal !important;
            word-wrap: break-word !important;
            max-width: none !important;
        }

            #grvProductosCompra .productimgname .view-info-product {
                white-space: normal !important;
                word-wrap: break-word !important;
                display: inline-block;
                max-width: 250px;
                line-height: 1.3;
            }

        .eliminar-producto {
            padding: 4px 8px;
            font-size: 12px;
        }

        .product-quantity {
            border: 1px solid rgba(145, 158, 171, 0.32);
            background-color: #fff;
            width: 80px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            padding: 0;
        }

            .product-quantity input {
                width: 28px;
                border: 0;
                background-color: #fff;
                text-align: center;
                height: 28px !important;
                font-size: 12px;
                padding: 0;
                margin: 0 2px;
            }

            .product-quantity span {
                color: #6c757d;
                font-size: 0;
                cursor: pointer;
                padding: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

                .product-quantity span svg {
                    stroke: 1px;
                    width: 14px;
                    height: 14px;
                }

        .view-product img {
            width: 25px;
            height: 25px;
            object-fit: cover;
            border-radius: 3px;
        }

        .view-info-product {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

            .view-info-product:hover {
                text-decoration: underline;
            }



        .quntity-input {
            width: 50px;
            text-align: center;
            border: none;
            background: transparent;
            margin: 0 5px;
        }

        /* Controlar altura de filas */
        #grvProductosCompra tbody tr {
            height: 35px;
        }

        /* Permitir texto completo en columna de producto */
        #grvProductosCompra .productimgname {
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: unset !important;
            max-width: none !important;
            min-width: 250px !important;
            width: 300px !important;
        }

        /* Evitar responsive collapse */
        #grvProductosCompra.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
        #grvProductosCompra.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
            display: none;
        }

        /* Validación de inputs */
        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }



        /* Estilos para currency y price (copiados de main.css) */
        tr td .currency {
            float: left;
        }

        tr td .price {
            float: right;
        }

        /* Estilos simples para la tabla */
        #grvProductosCompra {
            width: 100% !important;
        }

            #grvProductosCompra th,
            #grvProductosCompra td {
                vertical-align: middle !important;
                padding: 8px !important;
            }

            #grvProductosCompra .productimgname {
                white-space: nowrap !important;
            }

        @media (max-width: 768px) {
            #grvProductosCompra {
                min-width: 800px !important;
            }
        }

        .btn-precio-up, .btn-precio-down, .btn-precio-min-up, .btn-precio-min-down {
            padding: 1px 4px;
            font-size: 8px;
            line-height: 1;
            border: 1px solid #ced4da;
            background-color: #f8f9fa;
            height: 14px;
            width: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

            .btn-precio-up:hover, .btn-precio-down:hover,
            .btn-precio-min-up:hover, .btn-precio-min-down:hover {
                background-color: #e9ecef;
            }

        .input-group-append {
            display: flex;
            flex-direction: column;
        }

            .input-group-append .btn {
                border-radius: 0;
                border-left: none;
            }

                .input-group-append .btn:first-child {
                    border-top-right-radius: 0.25rem;
                }

                .input-group-append .btn:last-child {
                    border-bottom-right-radius: 0.25rem;
                }

        /* Ocultar iconos de ordenamiento del DataTable */
        #grvProductosCompra th.sorting:before,
        #grvProductosCompra th.sorting:after,
        #grvProductosCompra th.sorting_asc:before,
        #grvProductosCompra th.sorting_asc:after,
        #grvProductosCompra th.sorting_desc:before,
        #grvProductosCompra th.sorting_desc:after {
            display: none !important;
        }

        /* Estilos adicionales para que coincida con nueva.aspx */
        #grvProductosCompra .ammounts {
            text-align: center;
        }

        #grvProductosCompra .options-tables {
            text-align: center;
        }
    </style>

    <script type="text/javascript">
        // Variables globales
        var tbl_productos_compra;
        var tbl_proveedores;
        var productosCompra = [];

        $(document).ready(function () {
            $('#txtFechaCompra').val(new Date().toISOString().split('T')[0]);

            inicializarDataTableProductos();
            inicializarDataTableProveedores();
            inicializarDataTableProductosModal();
            cargarFormasPago();

            // Eventos
            $('#btnBuscarProveedor').on('click', function () {
                cargarProveedores();
            });



            $('#btnBuscarProducto').on('click', function () {
                cargarProductosModal();
            });

            $("#txtCodigoBarraProducto").on("click keydown", function (e) {
                if (e.type === "click" || (e.type === "keydown" && (e.which === 13 || e.which === 9))) {
                    e.preventDefault();
                    if ($("#txtCodigoBarraProducto").val().trim() !== "") {
                        fnValidaCodigoBarra();
                    }
                }
            });

            $("#txtCodigoBarraProducto").on("keydown", function (event) {
                if (event.key === "F1") {
                    event.preventDefault();
                    $("#btnBuscarProducto").click();
                }
            });

            $('#btnGuardarCompraFinal').on('click', function () {
                guardarCompra();
            });

            $('#btnGuardarNuevoProveedor').on('click', function () {
                guardarNuevoProveedor();
            });

            $('#modalCrearProveedor').on('hidden.bs.modal', function () {
                $('#txtNitNuevoProveedor').val('');
                $('#txtNombreNuevoProveedor').val('');
                $('#txtTelefonoNuevoProveedor').val('');
                $('#txtEmailNuevoProveedor').val('');
                $('#txtDireccionNuevoProveedor').val('');

                $('#btnGuardarNuevoProveedor').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                feather.replace();
            });

            $('#btnCancelarCompra').on('click', function () {
                Swal.fire({
                    title: '&iexcl;Est&aacute; seguro?',
                    html: 'Los datos ingresados no se guardar&aacute;n.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'S&iacute;, cancelar',
                    cancelButtonText: 'No'
                }).then((result) => {
                    if (result.isConfirmed) {
                        limpiarFormulario();
                        Swal.fire('Cancelado', 'Se han limpiado todos los campos.', 'success');
                    }
                });
            });

            $("#txtNitProveedor").on('keydown', function (e) {
                if (e.which == 13 || e.which == 9) {
                    e.preventDefault();
                    if ($(this).val().trim() != "") {
                        buscarProveedorPorNit($("#txtNitProveedor").val());
                    }
                }
            });

            $('#chkAplicaDuca').on('change', function () {
                if ($(this).is(':checked')) {
                    $('#fileFactura')
                        .closest('.col-12').show();
                    $('#txtPolizaDuca')
                        .closest('.col-12').show();
                    $('#ddlPaisOrigen')
                        .closest('.col-12').show();
                    $('#ddlPaisDestino')
                        .closest('.col-12').show();
                    $('#txtDucaNoOrden')
                        .closest('.col-12').show();
                    $('#txtDucaEntradaSalidaPartida')
                        .closest('.col-12').show();
                    $('#txtDucaDomicilioFiscal')
                        .closest('.col-12').show();

                    cargarPaises();

                } else {
                    $('#fileFactura')
                        .closest('.col-12').hide();
                    $('#txtPolizaDuca')
                        .closest('.col-12').hide();
                    $('#ddlPaisOrigen')
                        .closest('.col-12').hide();
                    $('#ddlPaisDestino')
                        .closest('.col-12').hide();
                    $('#txtDucaNoOrden')
                        .closest('.col-12').hide();
                    $('#txtDucaEntradaSalidaPartida')
                        .closest('.col-12').hide();
                    $('#txtDucaDomicilioFiscal')
                        .closest('.col-12').hide();

                    $('#ddlPaisOrigen').empty().append('<option value="">Pa&iacute;s de origen</option>');
                    $('#ddlPaisDestino').empty().append('<option value="">Pa&iacute;s de destino</option>').prop('disabled', false);
                }
            });


        });

        function inicializarDataTableProductos() {
            tbl_productos_compra = $('#grvProductosCompra').DataTable({
                "language": {
                    "lengthMenu": "_MENU_",
                    "sSearch": "",
                    "searchPlaceholder": "Buscar producto",
                    "sLoadingRecords": "Cargando registros...",
                    "info": " ",
                    "emptyTable": "No hay productos agregados a la compra",
                    "zeroRecords": "No se encontraron productos",
                    paginate: {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: false,
                paging: true,
                autoWidth: false,
                searching: false,
                ordering: false,
                responsive: false,
                columnDefs: [
                    {
                        targets: [0],
                        visible: false
                    },
                    {
                        targets: [1, 3],
                        className: 'text-center'
                    },
                    {
                        targets: [2],
                        className: 'productimgname'
                    },
                    {
                        targets: [3, 4, 5, 6, 7, 8],
                        className: 'ammounts'
                    },
                    {
                        targets: [9],
                        className: 'text-center options-tables'
                    }
                ],
                columns: [
                    { data: "id_producto" },
                    { data: "codigo" },
                    {
                        data: function (item) {
                            var img_producto;
                            if (!item.img_producto || item.img_producto === '' || item.img_producto === null) {
                                img_producto = '<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';
                            } else {
                                img_producto = item.img_producto;
                            }
                            return '<div class="view-product me-2">' +
                                '<a href="#!">' +
                                '<img src="' + img_producto + '" alt="" onerror="this.onerror=null;this.src=\'<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png\';">' +
                                '</a>' +
                                '</div>' +
                                '<a href="#!" class="view-info-product">' + item.nombre + '</a>';
                        }
                    },
                    {
                        data: function (item) {
                            return `<div class="product-quantity">
                                        <span class="quantity-btn" data-action="minus"><i data-feather="minus-circle" class="feather-search"></i></span>
                                        <input type="text" class="quntity-input cantidad-input" value="1" min="1" max="${item.stock_actual || 999}">
                                        <span class="quantity-btn" data-action="plus"><i data-feather="plus-circle" class="plus-circle"></i></span>
                                    </div>`;
                        }
                    },
                    { data: "stock_actual" },
                    {
                        data: function (item) {
                            var costoUnitario = item.costo_unitario || 0;
                            return '<input type="text" class="form-control form-control-sm text-center costo-input" value="Q ' + formatearNumero(costoUnitario.toFixed(2)) + '" style="width: 100px;">';
                        }
                    },
                    {
                        data: function (item) {
                            return `<span class="costo-total" style="font-size: 14px;">Q 0.00</span>`;
                        }
                    },
                    {
                        data: function (item) {
                            var precioVenta = item.precio_unitario || item.costo_unitario || 0;
                            return '<input type="text" class="form-control form-control-sm text-center precio-venta-input" value="Q ' + formatearNumero(precioVenta.toFixed(2)) + '" style="width: 110px;">';
                        }
                    },
                    {
                        data: function (item) {
                            var precioMinimo = item.min_descuento || item.costo_unitario || 0;
                            return '<input type="text" class="form-control form-control-sm text-center precio-min-input" value="Q ' + formatearNumero(precioMinimo.toFixed(2)) + '" style="width: 110px;">';
                        }
                    },
                    {
                        data: function (item) {
                            return '<button class="btn btn-danger btn-sm eliminar-producto" type="button" title="Eliminar producto"><i class="fa fa-trash"></i></button>';
                        }
                    }
                ],
                rowCallback: function (row, data) {
                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Datos del producto para modal:", data);
                        __InfoProduct(data);
                    });

                    var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 1;
                    var costoUnitarioTexto = $(row).find('.costo-input').val().replace('Q', '').trim();
                    var costoUnitario = parseFloat(limpiarFormatoNumero(costoUnitarioTexto)) || 0;
                    var costoTotal = cantidad * costoUnitario;
                    $(row).find('.costo-total').text('Q ' + formatearNumero(costoTotal.toFixed(2)));

                    $(row).find('.cantidad-input, .costo-input').on('input', function () {
                        var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
                        var costoUnitarioTexto = $(row).find('.costo-input').val().replace('Q', '').trim();
                        var costoUnitario = parseFloat(limpiarFormatoNumero(costoUnitarioTexto)) || 0;
                        var costoTotal = cantidad * costoUnitario;
                        $(row).find('.costo-total').text('Q ' + formatearNumero(costoTotal.toFixed(2)));

                        fnUpdateTotales();
                        validarYActualizarPrecios(row, costoUnitario);
                    });

                    $(row).find('.costo-input').on('blur', function () {
                        var valor = $(this).val().replace('Q', '').trim();
                        var numero = parseFloat(limpiarFormatoNumero(valor));
                        if (!isNaN(numero) && numero > 0) {
                            $(this).val('Q ' + formatearNumero(numero.toFixed(2)));
                        }
                    });

                    $(row).find('.precio-venta-input').on('blur', function () {
                        validarPreciosManual(this);
                    });

                    $(row).find('.precio-min-input').on('blur', function () {
                        validarPreciosManual(this);
                    });

                    $(row).find('.quantity-btn').off('click').on('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation();

                        console.log('Botón clickeado'); // Debug

                        var $button = $(this);
                        var $inputCantidad = $button.closest('.product-quantity').find('input.cantidad-input');
                        var cantidadActual = parseInt($inputCantidad.val()) || 1;
                        var stockDisponible = parseInt($inputCantidad.attr('max')) || 999;
                        var nuevaCantidad = cantidadActual;
                        var accion = $button.attr('data-action');

                        console.log('Cantidad actual:', cantidadActual); // Debug
                        console.log('Stock disponible:', stockDisponible); // Debug
                        console.log('Acción:', accion); // Debug

                        if (accion === 'plus') {
                            if (cantidadActual < stockDisponible) {
                                nuevaCantidad = cantidadActual + 1;
                                console.log('Incrementando a:', nuevaCantidad); // Debug
                            }
                        } else if (accion === 'minus') {
                            if (cantidadActual > 1) {
                                nuevaCantidad = cantidadActual - 1;
                            }
                        }

                        $inputCantidad.val(nuevaCantidad);

                        var costoUnitarioTexto = $(row).find('.costo-input').val().replace('Q', '').trim();
                        var costoUnitario = parseFloat(limpiarFormatoNumero(costoUnitarioTexto)) || 0;
                        var costoTotal = nuevaCantidad * costoUnitario;
                        $(row).find('.costo-total').text('Q ' + formatearNumero(costoTotal.toFixed(2)));

                        fnUpdateTotales();
                    });

                    $(row).find('.eliminar-producto').on('click', function () {
                        Swal.fire({
                            title: '&iquest;Eliminar producto?',
                            html: '&iquest;Est&aacute; seguro de que desea eliminar este producto de la compra?',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#d33',
                            cancelButtonColor: '#3085d6',
                            confirmButtonText: 'S&iacute;, eliminar',
                            cancelButtonText: 'Cancelar'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                $('#grvProductosCompra').DataTable().row(row).remove().draw();
                                fnUpdateTotales();

                                Swal.fire('Eliminado', 'El producto ha sido eliminado de la compra.', 'success');
                            }
                        });
                    });
                    feather.replace();
                }
            });

            setTimeout(function () {
                feather.replace();
                fnUpdateTotales();
            }, 100);
        }

        function inicializarDataTableProveedores() {
            tbl_proveedores = $('#grvProveedores').DataTable({
                "language": {
                    "lengthMenu": "_MENU_",
                    "sSearch": "",
                    "searchPlaceholder": "Buscar proveedor",
                    "sLoadingRecords": "Cargando registros...",
                    "info": " ",
                    paginate: {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: false,
                paging: true,
                autoWidth: false,
                columnDefs: [
                    {
                        targets: [0, 3, 4],
                        visible: false
                    }
                ],
                columns: [
                    { data: "id_proveedor" },
                    { data: "nit" },
                    { data: "nombres" },
                    { data: "telefono" },
                    {
                        data: function (item) {
                            return item.email || item.correo || '';
                        }
                    },
                    { data: "direccion" },
                    {
                        data: function (item) {
                            return `<td>
                                        <div class="hstack gap-2 fs-15">
                                            <a href="#!" class="btn btn-icon btn-sm btn-soft-success rounded-pill add-proveedor"><i class="fas fa-user-check"></i></a>
                                        </div>
                                    </td>`;
                        }
                    }
                ],
                createdRow: function (row, data, dataIndex) {
                    let $row = $(row);

                    $row.find(".add-proveedor").on("click", function () {
                        agregarProveedor($(this), data);
                    });

                    $row.on("dblclick", function () {
                        agregarProveedor($(this), data);
                    });
                }
            });
        }

        function cargarProveedores() {
            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: function () {
                    var fmAuth = new FormData();
                    fmAuth.append("mth", mtdEnc("get/proveedores"));
                    fmAuth.append("data", mtdEnc(JSON.stringify({})));
                    return fmAuth;
                }(),
                contentType: false,
                processData: false,
                beforeSend: function () {
                },
                success: function (result) {
                    if (result.type == "success") {
                        $('#grvProveedores').dataTable().fnClearTable();
                        $('#grvProveedores').DataTable().search("").draw();
                        $('#grvProveedores').dataTable().fnAddData(result.data);

                        $("#modalProveedores").modal("show");
                    }
                    else if (result.type == "warning") {
                        if (result.type === "warning") {
                            Swal.fire({
                                    icon: 'warning',
                                    title: 'Producto no encontrado',
                                    html: 'Producto no existente en el inventario, &iquest;Desea agregarlo?',
                                    showCancelButton: true,
                                    confirmButtonText: 'S&iquest;, agregar producto',
                                    cancelButtonText: 'No, cancelar',
                                    confirmButtonColor: '#FF9F43',
                                    cancelButtonColor: '#6c757d'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.href = '../Inventario/NuevoProducto.aspx';
                                }
                            });
                        } else {
                            Swal.fire({ icon: result.type, text: result.text });
                        }
                    }
                    else {
                        if (result.type === "warning") {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Producto no encontrado',
                                html: 'Producto no existente en el inventario, &iquest;Desea agregarlo?',
                                showCancelButton: true,
                                confirmButtonText: 'S&iquest;, agregar producto',
                                cancelButtonText: 'No, cancelar',
                                confirmButtonColor: '#FF9F43',
                                cancelButtonColor: '#6c757d'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.href = '../Inventario/NuevoProducto.aspx';
                                }
                            });
                        } else {
                            Swal.fire({ icon: result.type, text: result.text });
                        }
                    }
                },
                error: function () {
                    Swal.fire('Error', 'Ocurrió un error al cargar los proveedores.', 'error');
                }
            });
        }

        // Función para inicializar DataTable de productos del modal
        function inicializarDataTableProductosModal() {
            $("#grvProductosConsulta").DataTable({
                "language": {
                    "lengthMenu": "_MENU_",
                    "sSearch": "",
                    "searchPlaceholder": "Buscar producto",
                    "sLoadingRecords": "Cargando registros...",
                    "info": " ",
                    paginate: {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: true,
                paging: true,
                autoWidth: true,
                columnDefs: [
                    {
                        targets: 1,
                        className: 'productimgname'
                    },
                    {
                        targets: [2, 3, 4, 5, 6, 7, 8, 9],
                        className: 'text-center'
                    },
                    {
                        targets: [3],
                        className: 'text-right'
                    },
                    {
                        targets: [4, 5, 6],
                        visible: false
                    }
                ],
                columns: [
                    { data: "codigo" },
                    {
                        data: function (item) {
                            let img_producto = '';
                            if (!item.img_producto) {
                                img_producto = `<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png`;
                            } else {
                                img_producto = item.img_producto;
                            }
                            return `<div class="view-product me-2">
                                        <a href="#!">
                                            <img src="${img_producto}" alt="" onerror="this.onerror=null;this.src='<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';">
                                        </a>
                                    </div>
                                    <a href="#!" class="view-info-product">${item.nombre}</a>`;
                        }
                    },
                    {
                        data: function (item) {
                            return `<span class="currency">${item.moneda || 'Q'}</span>
                                    <span class="price">${(item.precio_unitario || 0).toFixed(2)}</span>`;
                        }
                    },
                    { data: "marca" },
                    { data: "modelo" },
                    { data: "anio" },
                    { data: "descripcion" },
                    { data: "stock_actual" },
                    {
                        data: function (item) {
                            return `<span class="tbl_lbl_canasta"></span>`;
                        }
                    },
                    {
                        data: function (item) {
                            if ((item.stock_actual || 0) > 0) {
                                return '<button class="btn btn-success btn-sm add-product" type="button" role="button" style="cursor: pointer;" title="Agregar producto"><span class="fa fa-plus"></span></button>';
                            } else {
                                return "";
                            }
                        }
                    },
                ],
                rowCallback: function (row, data) {
                    let info_producto = fnBuscarProductoCompra(data.id_producto);
                    let cantidad_canasta = info_producto.producto ? info_producto.producto.cantidad : 0;

                    data.cantidad_canasta = cantidad_canasta;
                    $(row).find(".tbl_lbl_canasta").html(data.cantidad_canasta);

                    if (data.stock_actual < 1 || data.cantidad_canasta >= data.stock_actual) {
                        $(row).addClass("no_stock").find(".add-product").hide();
                    }

                    $(row).find(".add-product").on("click", function () {
                        let info_producto = fnBuscarProductoCompra(data.id_producto);
                        let cantidad_canasta = info_producto.producto ? info_producto.producto.cantidad : 0;

                        if (cantidad_canasta > 0 && data.cantidad_canasta >= data.stock_actual) {
                            console.error("No hay suficiente stock.");
                            return;
                        }

                        cantidad_canasta++;
                        let $btnAddProduct = $(this);
                        $btnAddProduct.html('<span class="fas fa-spinner fa-spin"></span>');
                        fnAgregarProductoCompra(data);

                        setTimeout(function () {
                            $btnAddProduct.html('<span class="fa fa-plus"></span>');
                        }, 200);
                    });

                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Ver info producto:", data);
                    });
                }
            });
        }



        // Función para cargar productos del modal
        function cargarProductosModal() {
            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx?mth=' + mtdEnc("get/products"),
                data: null,
                type: "GET",
                contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                dataType: "json",
                beforeSend: function () {
                },
                success: function (result) {
                    if (result.type == "success") {
                        $('#grvProductosConsulta').dataTable().fnClearTable();
                        $('#grvProductosConsulta').DataTable().search("").draw();
                        $('#grvProductosConsulta').dataTable().fnAddData(result.data);

                        $("#modalProductos").modal("show");
                    }
                    else {
                        if (result.type === "warning") {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Producto no encontrado',
                                text: 'Producto no existente en el inventario, ¿Desea agregarlo?',
                                showCancelButton: true,
                                confirmButtonText: 'Sí, agregar producto',
                                cancelButtonText: 'No, cancelar',
                                confirmButtonColor: '#FF9F43',
                                cancelButtonColor: '#6c757d'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.href = '../Inventario/NuevoProducto.aspx';
                                }
                            });
                        } else {
                            Swal.fire({ icon: result.type, text: result.text });
                        }
                    }
                },
                error: function () {
                    Swal.fire('Error', 'Ocurrió un error al cargar los productos.', 'error');
                }
            });
        }

        function fnBuscarProductoCompra(p_id_producto, p_tipo_busqueda = "id") {
            const table = $('#grvProductosCompra').DataTable().rows().data().toArray();
            const index = table.findIndex(row => {
                if (p_tipo_busqueda === "id") {
                    return row.id_producto === p_id_producto;
                } else if (p_tipo_busqueda === "codigo") {
                    return row.codigo === p_id_producto;
                }
                return false;
            });

            return {
                producto: index !== -1 ? table[index] : null,
                index: index
            };
        }

        function fnAgregarProductoCompra(p_producto, p_cantidad = 1) {
            const table = $('#grvProductosCompra').DataTable();
            const info_producto = fnBuscarProductoCompra(p_producto.id_producto);
            const producto = info_producto.producto;
            const stock_actual = p_producto.stock_actual ?? p_producto.existencia;
            let _stock_insuficiente = false;

            if (producto) {
                const nueva_cantidad = producto.cantidad + p_cantidad;
                if (nueva_cantidad > stock_actual) {
                    _stock_insuficiente = true;
                } else {
                    producto.cantidad = nueva_cantidad;
                    table.row(info_producto.index).data(producto).draw(false);
                }
            } else {
                if (p_cantidad > stock_actual) {
                    _stock_insuficiente = true;
                } else {
                    const nuevo_producto = {
                        ...p_producto,
                        cantidad: p_cantidad,
                        stock_actual: stock_actual,
                        existencia: stock_actual
                    };
                    table.row.add(nuevo_producto).draw(false);
                }
            }

            if (_stock_insuficiente) {
                Swal.fire('Stock insuficiente', 'No hay suficiente stock para agregar este producto.', 'warning');
                return;
            }

            // Actualizar totales
            fnUpdateTotales();

            setTimeout(() => {
                feather.replace();
                table.columns.adjust().draw();
            }, 50);
        }

        // Función para formatear números con comas
        function formatearNumero(numero) {
            return numero.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // Función para limpiar formato de número (quitar comas)
        function limpiarFormatoNumero(numeroFormateado) {
            return numeroFormateado.toString().replace(/,/g, "");
        }

        // Funciones para formatear inputs de costo
        function formatearInputCosto(input) {
            var valor = limpiarFormatoNumero(input.value);
            if (!isNaN(valor) && valor !== '') {
                input.value = formatearNumero(parseFloat(valor).toFixed(2));
            }
        }

        function limpiarFormatoCosto(input) {
            input.value = limpiarFormatoNumero(input.value);
        }

        // Funciones para formatear inputs de precio
        function formatearInputPrecio(input) {
            var valor = limpiarFormatoNumero(input.value);
            if (!isNaN(valor) && valor !== '') {
                input.value = formatearNumero(parseFloat(valor).toFixed(2));
            }
        }

        function limpiarFormatoPrecio(input) {
            input.value = limpiarFormatoNumero(input.value);
        }

        // Funciones para formatear inputs de precio mínimo
        function formatearInputPrecioMin(input) {
            var valor = limpiarFormatoNumero(input.value);
            if (!isNaN(valor) && valor !== '') {
                input.value = formatearNumero(parseFloat(valor).toFixed(2));
            }
        }

        function limpiarFormatoPrecioMin(input) {
            input.value = limpiarFormatoNumero(input.value);
        }

        function fnUpdateTotales() {
            let subtotal = 0;
            let descuento = 0;

            $('#grvProductosCompra tbody tr').each(function () {
                var $row = $(this);
                var costoTotalTexto = $row.find('.costo-total').text().trim().replace('Q', '').trim();
                var costoTotal = parseFloat(limpiarFormatoNumero(costoTotalTexto)) || 0;
                subtotal += costoTotal;
            });

            const total = subtotal - descuento;

            $('.lbl-info-subtotal').text(formatearNumero(subtotal.toFixed(2)));
            $('.lbl-info-descuento').text(formatearNumero(descuento.toFixed(2)));
            $('.lbl-info-total').text(formatearNumero(total.toFixed(2)));
        }

        function fnValidaCodigoBarra() {
            let codigo_producto = $("#txtCodigoBarraProducto").val().trim();
            let cantidad = 1;

            if (codigo_producto.includes('*')) {
                let partes = codigo_producto.split('*');
                if (partes.length === 2 && !isNaN(partes[0]) && !isNaN(partes[1])) {
                    cantidad = parseInt(partes[0]);
                    codigo_producto = partes[1];
                }
            }

            if (codigo_producto) {
                let info_producto = fnBuscarProductoCompra(codigo_producto, "codigo");
                let producto = info_producto.producto;

                if (!producto) {
                    producto = fnGetProductByID(codigo_producto);
                }

                if (producto) {
                    fnAgregarProductoCompra(producto, cantidad);
                }
                $("#txtCodigoBarraProducto").val("");
            }
        }

        function fnGetProductByID(_codigoProducto) {
            var data;
            if (_codigoProducto != "") {
                $.ajax({
                    url: `<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx?mth=${mtdEnc("get/product/by/id")}&productCode=${mtdEnc(_codigoProducto)}`,
                    data: null,
                    type: "GET",
                    async: false,
                    contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                    dataType: "json",
                    beforeSend: function () {
                        // Mostrar loading
                    },
                    success: function (result) {
                        if (result.type == "success") {
                            data = result.data[0];
                        }
                        else {
                            // Verificar si es un mensaje de warning (producto no encontrado)
                            if (result.type === "warning") {
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Producto no encontrado',
                                    text: 'Producto no existente en el inventario, ¿Desea agregarlo?',
                                    showCancelButton: true,
                                    confirmButtonText: 'Sí, agregar producto',
                                    cancelButtonText: 'No, cancelar',
                                    confirmButtonColor: '#FF9F43',
                                    cancelButtonColor: '#6c757d'
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        window.location.href = '../Inventario/NuevoProducto.aspx';
                                    }
                                });
                            } else {
                                Swal.fire({ icon: result.type, text: result.text });
                            }
                        }
                    },
                    error: function (result) {
                        console.error('error:', result);
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurrió un error al buscar el producto.'
                        });
                    }
                });
            }
            else {
                Swal.fire({ icon: "warning", text: "Por favor ingrese el código del producto." });
            }

            return data;
        }

        function validarEmail(email) {
            var regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        }

        function guardarNuevoProveedor() {
            var nit = $('#txtNitNuevoProveedor').val().trim();
            var nombre = $('#txtNombreNuevoProveedor').val().trim();
            var telefono = $('#txtTelefonoNuevoProveedor').val().trim();
            var email = $('#txtEmailNuevoProveedor').val().trim();
            var direccion = $('#txtDireccionNuevoProveedor').val().trim();

            if (!nit || !nombre) {
                Swal.fire('Campos requeridos', 'El NIT y nombre del proveedor son obligatorios.', 'warning');
                return;
            }

            if (email && !validarEmail(email)) {
                Swal.fire('Email inválido', 'Por favor ingrese un email válido.', 'warning');
                return;
            }

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("create/provider"));
            fmAuth.append("nit", mtdEnc(nit));
            fmAuth.append("nombre", mtdEnc(nombre));
            fmAuth.append("telefono", mtdEnc(telefono));
            fmAuth.append("email", mtdEnc(email));
            fmAuth.append("direccion", mtdEnc(direccion));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                beforeSend: function () {
                    $('#btnGuardarNuevoProveedor').prop('disabled', true).html('<span class="fas fa-spinner fa-spin"></span>&nbsp;Guardando...');
                },
                success: function (response) {

                    if (response.type === "success" || (response.data && response.data.id_proveedor > 0)) {
                        var mensaje = 'El proveedor se cre\u00f3 correctamente.';
                        if (response.data && response.data.id_proveedor) {
                            mensaje += ' ID: ' + response.data.id_proveedor;
                        }
                        Swal.fire('', mensaje, 'success').then(() => {
                            $('#txtNitNuevoProveedor').val('');
                            $('#txtNombreNuevoProveedor').val('');
                            $('#txtTelefonoNuevoProveedor').val('');
                            $('#txtEmailNuevoProveedor').val('');
                            $('#txtDireccionNuevoProveedor').val('');

                            $('#modalCrearProveedor').modal('hide');

                            if (response.data && response.data.id_proveedor) {
                                $('#lblIdProveedor').text(response.data.id_proveedor);
                                $('#txtNitProveedor').val(response.data.nit);
                                $('#txtNombreProveedor').val(response.data.nombres);
                            } else {
                                setTimeout(function () {
                                    buscarProveedorPorNit(nit);
                                }, 500);
                            }
                        });
                    } else {
                        Swal.fire('Error', response.text || 'Ocurrió un error al crear el proveedor.', 'error');
                        $('#btnGuardarNuevoProveedor').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                    }
                },
                error: function () {
                    Swal.fire('Error', 'Ocurrió un error al crear el proveedor.', 'error');
                    $('#btnGuardarNuevoProveedor').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                },
                complete: function () {
                    feather.replace();
                }
            });
        }

        function cargarFormasPago() {
            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/medio/pago/sucursal"));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response.data_tables && Array.isArray(response.data_tables[0]) && response.data_tables.length > 0) {
                        $('#ddlFormaPago').empty().append('<option value="">Forma de pago</option>');

                        response.data_tables[0].forEach(function (item) {
                            console.log('Agregando opción:', item);
                            $('#ddlFormaPago').append('<option value="' + item.codigo + '">' + item.descripcion + '</option>');
                        });
                        console.log('Total opciones agregadas:', response.data_tables.length);
                    } else {
                        console.log('No se pudieron cargar las formas de pago - data_tables no encontrado o vacío');
                        console.log('Estructura de respuesta:', Object.keys(response));
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Error al cargar formas de pago:', error);
                    console.log('Status:', status);
                    console.log('XHR:', xhr);
                }
            });
        }

        function cargarPaises() {
            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/countries"));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response.type === "success") {
                        $('#ddlPaisOrigen').empty().append('<option value="">Pa&iacute;s de origen</option>');
                        $.each(response.data, function (index, item) {
                            $('#ddlPaisOrigen').append('<option value="' + item.id + '">' + item.descripcion + '</option>');
                        });

                        $('#ddlPaisDestino').empty().append('<option value="">Pa&iacute;s de destino</option>');
                        $.each(response.data, function (index, item) {
                            $('#ddlPaisDestino').append('<option value="' + item.id + '">' + item.descripcion + '</option>');
                        });

                        // Establecer Guatemala (id=1) como seleccionado y deshabilitar el dropdown de destino
                        $('#ddlPaisDestino').val('1').prop('disabled', true);
                    }
                },
                error: function () {
                    console.log('Error al cargar países');
                }
            });
        }

        function buscarProveedorPorNit(pNumeroNIT) {
            var nit = pNumeroNIT || $('#txtNitProveedor').val().trim();
            if (!nit) {
                Swal.fire('Campo requerido', 'Ingrese el NIT del proveedor.', 'warning');
                return;
            }

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/provider/by/nit"));
            fmAuth.append("nit", mtdEnc(nit));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response.type === "success" && response.data) {
                        $('#lblIdProveedor').text(response.data.id_proveedor);
                        $('#txtNombreProveedor').val(response.data.nombres);
                    } else {
                        Swal.fire({
                            title: 'Proveedor no encontrado',
                            html: '&iquest;Desea crear un nuevo proveedor con este NIT?',
                            icon: 'warning',
                            showDenyButton: true,
                            showCancelButton: false,
                            confirmButtonText: 'S&iacute;',
                            denyButtonText: 'No',
                            confirmButtonColor: '#FF9F43',
                            denyButtonColor: '#6c757d'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                $('#txtNitNuevoProveedor').val(nit);
                                $('#modalCrearProveedor').modal('show');
                            }
                        });
                        $('#txtNombreProveedor').val('');
                    }
                },
                error: function () {
                    Swal.fire('Error', 'Ocurrió; un error al buscar el proveedor.', 'error');
                }
            });
        }

        // Función para seleccionar proveedor del modal
        function seleccionarProveedor(id, nit, nombre) {
            $('#lblIdProveedor').text(id);
            $('#txtNitProveedor').val(nit);
            $('#txtNombreProveedor').val(nombre);
            $('#modalProveedores').modal('hide');
        }

        function agregarProveedor($btn, data) {
            $btn.html('<span class="fas fa-spinner fa-spin"></span>');
            seleccionarProveedor(data.id_proveedor, data.nit, data.nombres);

            setTimeout(function () {
                $btn.html('<i class="fas fa-user-check"></i>');
                $("#modalProveedores").modal("hide");
            }, 200);
        }

        function validarYActualizarPrecios(row, costoUnitario) {
            var $precioVentaInput = $(row).find('.precio-venta-input');
            var $precioMinInput = $(row).find('.precio-min-input');

            var precioVentaTexto = $precioVentaInput.val().trim().replace('Q', '').trim();
            var precioMinTexto = $precioMinInput.val().trim().replace('Q', '').trim();

            var precioVenta = parseFloat(limpiarFormatoNumero(precioVentaTexto)) || 0;
            var precioMin = parseFloat(limpiarFormatoNumero(precioMinTexto)) || 0;

            // PRECIO VENTA: Actualizar si está vacío, es 0, o es menor que el costo
            if (precioVentaTexto === '' || precioVenta === 0 || precioVenta < costoUnitario) {
                $precioVentaInput.val('Q ' + formatearNumero(costoUnitario.toFixed(2)));
                $precioVentaInput.css('border-color', '');
            } else {
                $precioVentaInput.css('border-color', '');
            }

            // PRECIO MÍNIMO: Actualizar si está vacío, es 0, o es menor que el costo
            if (precioMinTexto === '' || precioMin === 0 || precioMin < costoUnitario) {
                $precioMinInput.val('Q ' + formatearNumero(costoUnitario.toFixed(2)));
                $precioMinInput.css('border-color', '');
            } else {
                $precioMinInput.css('border-color', '');
            }
        }

        // Función para validar precios cuando el usuario TERMINA de escribir (solo blur)
        function validarPreciosManual(input) {
            var $row = $(input).closest('tr');
            var costoUnitarioTexto = $row.find('.costo-input').val().replace('Q', '').trim();
            var precioTexto = $(input).val().replace('Q', '').trim();

            var costoUnitario = parseFloat(limpiarFormatoNumero(costoUnitarioTexto)) || 0;
            var precio = parseFloat(limpiarFormatoNumero(precioTexto)) || 0;

            // Solo validar si el usuario escribió algo
            if (precioTexto !== '' && precio > 0) {
                if (costoUnitario > 0 && precio < costoUnitario) {
                    // Mostrar alerta y luego setear el valor al costo
                    Swal.fire({
                        icon: 'warning',
                        title: 'Precio menor al costo',
                        text: 'El precio que ingreso (Q ' + formatearNumero(precio.toFixed(2)) + ') es menor al costo del producto (Q ' + formatearNumero(costoUnitario.toFixed(2)) + ')',
                        showConfirmButton: true,
                        confirmButtonText: 'Entendido'
                    }).then(() => {
                        // Después del modal, setear automáticamente el valor al costo
                        $(input).val('Q ' + formatearNumero(costoUnitario.toFixed(2)));
                        $(input).css('border-color', ''); // Quitar borde rojo ya que ahora es válido
                    });

                    // Temporalmente mostrar el valor incorrecto con borde rojo
                    $(input).val('Q ' + formatearNumero(precio.toFixed(2)));
                    $(input).css('border-color', 'red'); // Borde rojo para indicar problema
                } else {
                    // Formatear el precio con Q y comas (precio válido)
                    $(input).val('Q ' + formatearNumero(precio.toFixed(2)));
                    $(input).css('border-color', ''); // Quitar borde rojo
                }
            } else if (precioTexto === '' || precio === 0) {
                // Si está vacío o es 0, poner el valor del costo
                if (costoUnitario > 0) {
                    $(input).val('Q ' + formatearNumero(costoUnitario.toFixed(2)));
                }
                $(input).css('border-color', '');
            }
        }

        function validarCantidadCosto(input) {
            var $row = $(input).closest('tr');
            var cantidad = parseFloat($row.find('.quntity-input').val()) || 0;
            var costoUnitarioTexto = $row.find('.costo-input').val().replace('Q', '').trim();
            var costoUnitario = parseFloat(limpiarFormatoNumero(costoUnitarioTexto)) || 0;
            var costoTotal = cantidad * costoUnitario;
            $row.find('.costo-total').text('Q ' + formatearNumero(costoTotal.toFixed(2)));

            fnUpdateTotales();
        }



        function obtenerMonedaPrincipal() {
            const table = $('#grvProductosCompra').DataTable();
            const productos = table.rows().data().toArray();
            //pendiente
            if (productos.length === 0) {
                return 'Q';
            }

            const monedas = productos.map(producto => producto.moneda || 'Q');

            const conteoMonedas = {};
            monedas.forEach(moneda => {
                conteoMonedas[moneda] = (conteoMonedas[moneda] || 0) + 1;
            });

            return Object.keys(conteoMonedas).reduce((a, b) =>
                conteoMonedas[a] > conteoMonedas[b] ? a : b
            );
        }

        function guardarCompra() {
            if (!$('#lblIdProveedor').text() || $('#lblIdProveedor').text() === '') {
                Swal.fire('Campo requerido', 'Seleccione un proveedor.', 'warning');
                return;
            }

            if (!$('#txtFechaCompra').val()) {
                Swal.fire('Campo requerido', 'Ingrese la fecha de compra.', 'warning');
                return;
            }

            if (!$('#txtCodigoCompra').val()) {
                Swal.fire('Campo requerido', 'Ingrese el código de la compra.', 'warning');
                return;
            }

            const table = $('#grvProductosCompra').DataTable();
            const productos = table.rows().data().toArray();

            if (productos.length === 0) {
                Swal.fire('Sin productos', 'Debe agregar al menos un producto a la compra.', 'warning');
                return;
            }

            if ($('#chkAplicaDuca').is(':checked')) {

                if (!$('#txtPolizaDuca').val()) {
                    Swal.fire('Campo requerido', 'Ingrese el n&uacute;mero de DUCA.', 'warning');
                    return;
                }

                if (!$('#ddlPaisOrigen').val()) {
                    Swal.fire('Campo requerido', 'Seleccione el pa&iacute;s de origen.', 'warning');
                    return;
                }

                if (!$('#ddlPaisDestino').val()) {
                    Swal.fire('Campo requerido', 'Seleccione el pa&iacute;s de destino.', 'warning');
                    return;
                }

                if (!$('#txtDucaNoOrden').val()) {
                    Swal.fire('Campo requerido', 'Ingrese el n&uacute;mero de orden.', 'warning');
                    return;
                }

                if (!$('#txtDucaEntradaSalidaPartida').val()) {
                    Swal.fire('Campo requerido', 'Ingrese entrada/salida/partida.', 'warning');
                    return;
                }

                if (!$('#txtDucaDomicilioFiscal').val()) {
                    Swal.fire('Campo requerido', 'Ingrese el domicilio fiscal.', 'warning');
                    return;
                }

            }

            // Preparar datos de la compra
            const datosCompra = {
                id_compra: 0,
                id_contribuyente: 1,
                id_sucursal: 1, 
                id_usuario: null, 
                id_moneda: 1,
                moneda: 'Q',
                mto_total: calcularTotal(),
                imp_total_txt: '', 
                productos: productos.map(producto => ({
                    id_producto: producto.id_producto,
                    id_moneda: 1,
                    codigo: producto.codigo,
                    nombre: producto.nombre,
                    cantidad: parseInt(producto.cantidad) || 1,
                    moneda: 'Q',
                    precio_venta: parseFloat(producto.precio_unitario) || 0,
                    costo_unitario: parseFloat(producto.costo_unitario) || 0,
                    descripcion: producto.descripcion || '',
                    categoria: producto.categoria || '',
                    estado: true,
                    existencia: producto.stock_actual || 0,
                    precio_unitario: parseFloat(producto.precio_unitario) || 0,
                    precio_minimo: parseFloat(producto.min_descuento) || 0,
                    recargo: 0,
                    descuento: 0,
                    img_producto: producto.img_producto || ''
                })),
                medios_pago: [{
                    codigo: $('#ddlFormaPago').val() || 'EFE',
                    descripcion: $('#ddlFormaPago option:selected').text() || 'Efectivo',
                    moneda: 'Q',
                    id_medio_pago: parseInt($('#ddlFormaPago').val()) || 1,
                    cod_entidad: null,
                    cod_referencia: null,
                    id_banco: null,
                    fecha_referencia: new Date().toISOString(),
                    num_referencia: null,
                    num_autorizacion: null,
                    monto: calcularTotal()
                }],
                encabezado: {
                    fecha: $('#txtFechaCompra').val(),
                    num_factura: $('#txtCodigoCompra').val() || '',//preguntar
                    num_serie: '',//preguntar
                    id_proveedor: parseInt($('#lblIdProveedor').text()) || 0,
                    //num_poliza: $('#chkAplicaDuca').is(':checked') ? ($('#txtPolizaDuca').val() || '') : '',       // $('#txtPolizaDuca').val() || '1',//PREGUNTAR SI ES EL DUCA NUMERO O CUAL?
                    num_poliza: null  ,       // $('#txtPolizaDuca').val() || '1',//PREGUNTAR SI ES EL DUCA NUMERO O CUAL?
                    aplica_duca: $('#chkAplicaDuca').is(':checked'),
                    id_pais_origen: $('#chkAplicaDuca').is(':checked') ? (parseInt($('#ddlPaisOrigen').val()) || null) : null,
                    id_pais_destino: $('#chkAplicaDuca').is(':checked') ? (parseInt($('#ddlPaisDestino').val()) || null) : null,
                    duca_no_orden: $('#chkAplicaDuca').is(':checked') ? ($('#txtDucaNoOrden').val() || null) : null,
                    duca_entrada_salida_partida: $('#chkAplicaDuca').is(':checked') ? ($('#txtDucaEntradaSalidaPartida').val() || null) : null,
                    duca_numero: $('#chkAplicaDuca').is(':checked') ? ($('#txtPolizaDuca').val() || null) : null,
                    duca_domicilio_fiscal: $('#chkAplicaDuca').is(':checked') ? ($('#txtDucaDomicilioFiscal').val() || null) : null
                }
            };

            var fmCompra = new FormData();
            fmCompra.append("mth", mtdEnc("save/purchase"));
            fmCompra.append("data", mtdEnc(JSON.stringify(datosCompra)));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                type: 'POST',
                data: fmCompra,
                contentType: false,
                processData: false,
                beforeSend: function () {
                    $('#btnGuardarCompraFinal').prop('disabled', true).html('<span class="fas fa-spinner fa-spin"></span>&nbsp;Guardando...');
                },
                success: function (response) {
                    if (response.type === "success") {
                        Swal.fire('&Eacute;xito', response.text || 'Compra guardada correctamente.', 'success').then(() => {
                            // Limpiar formulario
                            limpiarFormulario();
                        });
                    } else {
                        Swal.fire('Error', response.text || 'Ocurrió un error al guardar la compra.', 'error');
                    }
                },
                error: function () {
                    Swal.fire('Error', 'Ocurrió un error al guardar la compra.', 'error');
                },
                complete: function () {
                    $('#btnGuardarCompraFinal').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                    feather.replace();
                }
            });
        }

        // Función para calcular subtotal
        function calcularSubtotal() {
            const table = $('#grvProductosCompra').DataTable();
            const productos = table.rows().data().toArray();
            let subtotal = 0;

            productos.forEach(producto => {
                const cantidad = parseInt(producto.cantidad) || 1;
                const precio = parseFloat(producto.costo_unitario) || 0;
                subtotal += cantidad * precio;
            });

            return subtotal;
        }

        function calcularTotal() {
            return calcularSubtotal();
        }

        function limpiarFormulario() {
            $('#txtNitProveedor').val('');
            $('#txtNombreProveedor').val('');
            $('#lblIdProveedor').text('');

            $('#txtFechaCompra').val(new Date().toISOString().split('T')[0]); // Mantener fecha actual
            $('#txtCodigoCompra').val('');
            $('#txtPolizaDuca').val('');
            $('#ddlFormaPago').val('').trigger('change');

            // Limpiar campos DUCA
            $('#chkAplicaDuca').prop('checked', false);
            $('#txtDucaNoOrden').val('');
            $('#txtDucaEntradaSalidaPartida').val('');
            $('#txtDucaDomicilioFiscal').val('');
            $('#ddlPaisOrigen').empty().append('<option value="">País de origen</option>');
            $('#ddlPaisDestino').empty().append('<option value="">País de destino</option>').prop('disabled', false);

            // Limpiar archivo de factura y variable base64
            $('#fileFactura').val('');
            facturaBase64 = '';

            // Ocultar campos DUCA
            $('#fileFactura').closest('.col-12').hide();
            $('#txtPolizaDuca').closest('.col-12').hide();
            $('#ddlPaisOrigen').closest('.col-12').hide();
            $('#ddlPaisDestino').closest('.col-12').hide();
            $('#txtDucaNoOrden').closest('.col-12').hide();
            $('#txtDucaEntradaSalidaPartida').closest('.col-12').hide();
            $('#txtDucaDomicilioFiscal').closest('.col-12').hide();

            $('#txtCodigoBarraProducto').val('');

            $('#txtBuscarProveedor').val('');
            $('#txtBuscarProducto').val('');

            $('#grvProductosCompra').DataTable().clear().draw();

            if ($.fn.DataTable.isDataTable('#grvProveedores')) {
                $('#grvProveedores').DataTable().clear().draw();
            }

            if ($.fn.DataTable.isDataTable('#grvProductos')) {
                $('#grvProductos').DataTable().clear().draw();
            }

            fnUpdateTotales();

            $('.form-control').removeClass('is-invalid');
            $('.invalid-feedback').hide();

            $('.modal').modal('hide');
        }

        // Variable global para almacenar el base64 de la factura
        var facturaBase64 = '';

        // Función para manejar la carga de archivo y convertir a base64
        function handleFileUpload(input) {
            const file = input.files[0];
            if (file) {
                // Validar tamaño del archivo (máximo 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    Swal.fire('Error', 'El archivo es demasiado grande. Máximo 5MB permitido.', 'error');
                    input.value = '';
                    facturaBase64 = '';
                    return;
                }

                // Validar tipo de archivo
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
                if (!allowedTypes.includes(file.type)) {
                    Swal.fire('Error', 'Tipo de archivo no permitido. Solo se permiten imágenes (JPG, PNG, GIF) y PDF.', 'error');
                    input.value = '';
                    facturaBase64 = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    facturaBase64 = e.target.result;
                    console.log('Archivo convertido a base64:', facturaBase64.substring(0, 100) + '...');

                    // Mostrar mensaje de éxito
                    Swal.fire({
                        icon: 'success',
                        title: 'Archivo cargado',
                        text: `Archivo "${file.name}" cargado correctamente.`,
                        timer: 2000,
                        showConfirmButton: false
                    });
                };
                reader.onerror = function() {
                    Swal.fire('Error', 'Error al leer el archivo.', 'error');
                    input.value = '';
                    facturaBase64 = '';
                };
                reader.readAsDataURL(file);
            } else {
                facturaBase64 = '';
            }
        }
    </script>

</asp:Content>
